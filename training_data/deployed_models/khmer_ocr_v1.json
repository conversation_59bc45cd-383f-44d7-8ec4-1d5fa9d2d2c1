{"model_id": "khmer_ocr_v1_20250527", "model_name": "Khmer OCR Enhanced Model v1", "version": "1.0.0", "created_at": "2025-05-27T17:04:42.331793", "training_session_id": "071bb701-0c19-4400-98fb-82e64b3a1feb", "performance_metrics": {"accuracy": 0.6665911437113099, "precision": 0.6332615865257444, "recall": 0.6132638522144052, "f1_score": 0.6199297636515182, "confidence_score": 0.5866002064659527, "processing_time": 2.0, "improvement_over_baseline": 0.054427663887150035}, "training_data": {"total_samples": 6, "training_iterations": 5, "best_iteration": 3, "focus_areas": ["general_improvement", "extreme_enhancement"]}, "model_config": {"enhanced_preprocessing": true, "ai_enhancement": true, "robust_parsing": true, "khmer_optimized": true, "quality_threshold": 0.3, "confidence_threshold": 0.8}, "deployment_ready": true, "status": "trained", "notes": "Model trained on Cambodian ID cards with focus on extreme enhancement for low-quality images"}